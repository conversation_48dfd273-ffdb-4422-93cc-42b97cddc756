# 百度地图路线动画演示系统

这是一个基于百度地图的交互式路线规划和动画演示系统，支持多种出行方式和自定义路线动画。

## 项目启动

1. 替换 `/src/index.html` 下的 ak 的值（百度地图API密钥）
2. `npm install` 安装依赖
3. `npm start` 启动项目（默认端口8888，如被占用会自动使用8889）

## 新增功能

### 🎛️ 右侧工具栏
- **工具栏切换**：点击右侧齿轮图标打开/关闭工具栏
- **响应式设计**：工具栏支持滚动，适配不同屏幕尺寸

### 📍 点位管理
- **添加点位**：输入地名自动搜索并添加到地图
- **点位列表**：显示所有已添加的点位信息
- **拖拽排序**：支持拖拽调整点位顺序
- **点位编辑**：
  - 修改点位名称
  - 调整图标大小（24-72px）
  - 自定义图标颜色
- **删除点位**：删除点位时自动清理相关路径

### 🛣️ 路径管理
- **添加路径**：在任意两个点位间创建路径
- **出行方式**：
  - 🚗 驾车
  - ✈️ 飞行（贝塞尔曲线）
  - 🚌 公交
  - 🚴 骑行
  - 🚶 步行
- **路径颜色**：自定义路径颜色
- **路径列表**：显示所有路径信息
- **删除路径**：单独删除指定路径

### 🎨 贝塞尔曲线优化
- **增强曲率**：默认曲率从0.2提升到0.5
- **动态调整**：距离越远弧度越明显
- **实时控制**：滑块调节曲率参数（0.1-1.5）
- **即时刷新**：调整后立即查看效果

## 使用方法

1. **添加点位**：
   - 点击右侧齿轮图标打开工具栏
   - 在"添加点位"输入框中输入地名
   - 点击"添加"按钮或按回车键

2. **编辑点位**：
   - 在点位列表中点击"编辑"按钮
   - 修改名称、大小、颜色等属性
   - 点击"保存"确认修改

3. **调整顺序**：
   - 直接拖拽点位列表项进行排序

4. **添加路径**：
   - 选择起点和终点
   - 选择出行方式
   - 选择路径颜色
   - 点击"添加路径"

5. **观看动画**：
   - 点击左上角"动画"按钮开始播放
   - 使用"暂停"/"继续"控制播放
   - 调整曲率滑块改变飞行路线弧度

## 技术特性

- **模块化架构**：清晰的代码组织结构
- **实时更新**：所有修改立即反映到地图
- **数据持久化**：内存中维护完整的地图数据
- **错误处理**：完善的错误提示和异常处理
- **用户体验**：直观的拖拽操作和模态对话框

## 源码来源

TrackAnimation.js 源码来自：
https://github.com/huiyan-fe/BMapGLLib/blob/master/TrackAnimation/src/TrackAnimation.js

Lushu.js 源码来自：
https://bj.bcebos.com/v1/mapopen/github/BMapGLLib/Lushu/src/Lushu.js

TODO：
1. 界面操作
- 添加点位
  - 地址根据关键字检索，弹出下拉，然后选择地址
  - 地址添加到点位
- 添加路线
  - 选择点位，然后添加路线
- 修改点位
  - 修改点位的图标、大小、信息窗等
- 修改路线
  - 修改路线的出行方式、颜色等
2. 组操作
- 添加点位到组
- 添加路线到组
- 组排序
- 组内点位、路线排序
3. 动画操作
- 增加 TrackAnimation 的方式
  - TrackAnimation 增加 marker 的移动
  - TrackAnimation 增加暂停、继续等操作
- 修改动画
  - 修改动画点位、路线的顺序，每一步的时长等
4. 导入导出
- 导出全部为文件
- 导入文件
