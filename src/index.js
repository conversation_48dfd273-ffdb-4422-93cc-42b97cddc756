import {ALL_DATA_ID, initData} from './func/dataFunc';
import init from './func/init';
import trip from './trip/trip';
import draw from './draw/draw';
import drawAni from './draw/drawAni';
import Toolbar from './toolbar/toolbar';

// 点位/路线的id
window.ELEMENTID = 0;
// 贝塞尔曲线的曲率
window.CURVATURE = 0.2;
// 贝塞尔曲线，取的点位数
window.NUMPOINTS = 150;
// marker 大小
window.MARKER_SIZE = 48;
// 图标大小
window.ICONSIZE = 48;
// 图标大小 anchor
window.ICONSIZEANCHOR = 24;
// 动画时间，5s
window.ANI_TIME = 5;
// 动画时间倍率，一个魔法值，经验值
window.ANI_TIME_RATIO = 1.6;
// 路线点位数组，最大长度
window.MAX_ELEMENTS = 1000;
// 正在展示动画。控制同一时间只能有一条动画正在展示
window.isDrawingAni = false;
// 路书动画，放到全局，方便控制暂停和继续
window.LUSHU;
// 可选颜色。翡翠绿、皇家蓝、深红色、深橙色、青色、珊瑚色、天蓝色、绿松石、宝蓝色、靛蓝、明亮紫色、鲜红色
window.COLORS = ['#50C878','#4169E1','#DC143C','#FF8C00',
  '#00FFFF','#FF7F50','#87CEEB','#40E0D0',
  '#0033CC','#4B0082','#9B30FF','#FF4500'];

document.addEventListener('DOMContentLoaded', async () => {
  // 百度地图渲染后，这个事件还会触发一次
  if (window.BMAP) return;

  // 存储 {pointId : point} 坐标
  window.pointsMap = {};
  // 存储 {pointId : info} 信息窗口
  window.infoWindowsMap = {};

  // 地图初始化
  init();
  // 数据重置
  initData();
  // 设置行程
  await trip();
  // 地图上绘制
  draw(TRIPS[ALL_DATA_ID].data);

  // 初始化工具栏
  window.toolbar = new Toolbar();

  document.getElementById('btn_start').onclick = function () {
    BMAP.clearOverlays();
    drawAni(TRIPS[ALL_DATA_ID].data);
  }
  document.getElementById('btn_pause').onclick = function () {
    LUSHU.pause();
  }
  document.getElementById('btn_resume').onclick = function () {
    LUSHU.start();
  }
});
