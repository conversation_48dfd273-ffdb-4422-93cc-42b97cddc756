import { ALL_DATA_ID, addPoint, addRoute, removePoint, removeRoute } from '../func/dataFunc';
import { getRandomColor, getRandomInt } from '../func/IDGenerator';
import draw from '../draw/draw';

class Toolbar {
  constructor() {
    this.isOpen = false;
    this.selectedPoints = [];
    this.init();
  }

  init() {
    this.bindEvents();
    this.updatePointsList();
    this.updateRoutesList();
    this.updateRouteSelects();
  }

  bindEvents() {
    // 工具栏切换
    document.getElementById('toolbar-toggle').addEventListener('click', () => {
      this.toggle();
    });

    // 添加点位
    document.getElementById('add-point-btn').addEventListener('click', () => {
      this.addPoint();
    });

    // 添加路径
    document.getElementById('add-route-btn').addEventListener('click', () => {
      this.addRoute();
    });

    // 回车键添加点位
    document.getElementById('point-name').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.addPoint();
      }
    });

    // 出行方式变化时显示/隐藏曲率控制
    document.getElementById('route-way').addEventListener('change', (e) => {
      this.toggleCurvatureControl(e.target.value);
    });

    // 曲率滑块变化
    document.getElementById('route-curvature').addEventListener('input', (e) => {
      document.getElementById('curvature-display').textContent = e.target.value;
    });
  }

  toggle() {
    const toolbar = document.getElementById('toolbar');
    const toggleBtn = document.getElementById('toolbar-toggle');

    this.isOpen = !this.isOpen;

    if (this.isOpen) {
      toolbar.classList.add('open');
      toggleBtn.innerHTML = '<span>✕</span>';
    } else {
      toolbar.classList.remove('open');
      toggleBtn.innerHTML = '<span>⚙</span>';
    }
  }

  async addPoint() {
    const nameInput = document.getElementById('point-name');
    const name = nameInput.value.trim();

    if (!name) {
      alert('请输入地点名称');
      return;
    }

    try {
      const pointId = await addPoint({
        name: name,
        opts: {
          iconSize: MARKER_SIZE,
          iconColor: getRandomColor()
        }
      });

      nameInput.value = '';
      this.updatePointsList();
      this.updateRouteSelects();
      this.refreshMap();

      console.log('点位添加成功:', pointId);
    } catch (error) {
      alert('添加点位失败: ' + error.message);
    }
  }

  toggleCurvatureControl(way) {
    const curvatureGroup = document.getElementById('curvature-group');
    if (way === 'fly') {
      curvatureGroup.style.display = 'block';
    } else {
      curvatureGroup.style.display = 'none';
    }
  }

  addRoute() {
    const startSelect = document.getElementById('route-start');
    const endSelect = document.getElementById('route-end');
    const waySelect = document.getElementById('route-way');
    const colorInput = document.getElementById('route-color');
    const curvatureInput = document.getElementById('route-curvature');

    const startPointId = parseInt(startSelect.value);
    const endPointId = parseInt(endSelect.value);
    const way = waySelect.value;
    const color = colorInput.value;
    const curvature = parseFloat(curvatureInput.value);

    if (!startPointId || !endPointId) {
      alert('请选择起点和终点');
      return;
    }

    if (startPointId === endPointId) {
      alert('起点和终点不能相同');
      return;
    }

    try {
      const routeOpts = {
        strokeColor: color,
        strokeWeight: 5,
        strokeOpacity: 0.8,
        lushuTip: `${way === 'fly' ? '飞行' : way === 'drive' ? '驾车' : way === 'bus' ? '公交' : way === 'ride' ? '骑行' : '步行'}路线`
      };

      // 如果是飞行路线，添加曲率参数
      if (way === 'fly') {
        routeOpts.curvature = curvature;
      }

      const routeId = addRoute({
        startPoint: startPointId,
        endPoint: endPointId,
        way: way,
        opts: routeOpts
      });

      // 重置表单
      startSelect.value = '';
      endSelect.value = '';
      waySelect.value = 'drive';
      colorInput.value = '#1b8eec';
      curvatureInput.value = '0.2';
      document.getElementById('curvature-display').textContent = '0.2';
      this.toggleCurvatureControl('drive'); // 隐藏曲率控制

      this.updateRoutesList();
      this.refreshMap();

      console.log('路径添加成功:', routeId);
    } catch (error) {
      alert('添加路径失败: ' + error.message);
    }
  }

  updatePointsList() {
    const pointsList = document.getElementById('points-list');
    const points = TRIPS[ALL_DATA_ID].data.points;

    pointsList.innerHTML = '';

    points.forEach((point, index) => {
      const listItem = this.createPointListItem(point, index);
      pointsList.appendChild(listItem);
    });
  }

  createPointListItem(point, index) {
    const div = document.createElement('div');
    div.className = 'list-item';
    div.draggable = true;
    div.dataset.pointId = point.id;

    div.innerHTML = `
      <div class="list-item-header">
        <span class="list-item-title">${point.name}</span>
        <div class="list-item-actions">
          <button class="btn-secondary" onclick="toolbar.editPoint(${point.id})">编辑</button>
          <button class="btn-danger" onclick="toolbar.deletePoint(${point.id})">删除</button>
        </div>
      </div>
      <div style="font-size: 12px; color: #666;">
        经度: ${point.geo.lng.toFixed(4)}, 纬度: ${point.geo.lat.toFixed(4)}
      </div>
    `;

    // 添加拖拽事件
    this.addDragEvents(div);

    return div;
  }

  updateRoutesList() {
    const routesList = document.getElementById('routes-list');
    const routes = TRIPS[ALL_DATA_ID].data.routes;

    routesList.innerHTML = '';

    routes.forEach((route, index) => {
      const listItem = this.createRouteListItem(route, index);
      routesList.appendChild(listItem);
    });
  }

  createRouteListItem(route, index) {
    const div = document.createElement('div');
    div.className = 'list-item';
    div.dataset.routeId = route.id;

    const points = TRIPS[ALL_DATA_ID].data.points;
    const startPoint = points.find(p => p.id === route.startPoint);
    const endPoint = points.find(p => p.id === route.endPoint);

    const wayNames = {
      'drive': '驾车',
      'fly': '飞行',
      'bus': '公交',
      'ride': '骑行',
      'walk': '步行'
    };

    const curvatureInfo = route.way === 'fly' && route.opts?.curvature
      ? ` | 曲率: ${route.opts.curvature}`
      : '';

    div.innerHTML = `
      <div class="list-item-header">
        <span class="list-item-title">${startPoint?.name || '未知'} → ${endPoint?.name || '未知'}</span>
        <div class="list-item-actions">
          <button class="btn-danger" onclick="toolbar.deleteRoute(${route.id})">删除</button>
        </div>
      </div>
      <div style="font-size: 12px; color: #666;">
        方式: ${wayNames[route.way] || route.way}${curvatureInfo}
        <span style="display: inline-block; width: 20px; height: 12px; background: ${route.opts?.strokeColor || '#1b8eec'}; margin-left: 8px; border-radius: 2px;"></span>
      </div>
    `;

    return div;
  }

  updateRouteSelects() {
    const startSelect = document.getElementById('route-start');
    const endSelect = document.getElementById('route-end');
    const points = TRIPS[ALL_DATA_ID].data.points;

    // 清空现有选项
    startSelect.innerHTML = '<option value="">选择起点</option>';
    endSelect.innerHTML = '<option value="">选择终点</option>';

    // 添加点位选项
    points.forEach(point => {
      const option1 = document.createElement('option');
      option1.value = point.id;
      option1.textContent = point.name;
      startSelect.appendChild(option1);

      const option2 = document.createElement('option');
      option2.value = point.id;
      option2.textContent = point.name;
      endSelect.appendChild(option2);
    });
  }

  deletePoint(pointId) {
    if (confirm('确定要删除这个点位吗？')) {
      removePoint(pointId);
      this.updatePointsList();
      this.updateRouteSelects();
      this.refreshMap();
    }
  }

  deleteRoute(routeId) {
    if (confirm('确定要删除这条路径吗？')) {
      removeRoute(routeId);
      this.updateRoutesList();
      this.refreshMap();
    }
  }

  editPoint(pointId) {
    const points = TRIPS[ALL_DATA_ID].data.points;
    const point = points.find(p => p.id === pointId);

    if (!point) {
      alert('点位不存在');
      return;
    }

    // 创建编辑对话框
    const modal = this.createEditModal(point);
    document.body.appendChild(modal);
  }

  createEditModal(point) {
    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 2000;
      display: flex;
      align-items: center;
      justify-content: center;
    `;

    const dialog = document.createElement('div');
    dialog.style.cssText = `
      background: white;
      padding: 20px;
      border-radius: 8px;
      width: 400px;
      max-width: 90%;
    `;

    dialog.innerHTML = `
      <h3 style="margin-top: 0;">编辑点位: ${point.name}</h3>

      <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px;">点位名称:</label>
        <input type="text" id="edit-point-name" value="${point.name}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
      </div>

      <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px;">图标大小:</label>
        <input type="range" id="edit-point-size" min="24" max="72" value="${point.opts?.iconSize || MARKER_SIZE}" style="width: 100%;">
        <span id="size-display">${point.opts?.iconSize || MARKER_SIZE}px</span>
      </div>

      <div style="margin-bottom: 15px;">
        <label style="display: block; margin-bottom: 5px;">图标颜色:</label>
        <input type="color" id="edit-point-color" value="${point.opts?.iconColor || '#1b8eec'}" style="width: 60px; height: 40px; border: none; border-radius: 4px;">
      </div>

      <div style="display: flex; gap: 10px; justify-content: flex-end;">
        <button id="cancel-edit" style="padding: 8px 16px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer;">取消</button>
        <button id="save-edit" style="padding: 8px 16px; border: none; background: #1b8eec; color: white; border-radius: 4px; cursor: pointer;">保存</button>
      </div>
    `;

    modal.appendChild(dialog);

    // 绑定事件
    const sizeSlider = dialog.querySelector('#edit-point-size');
    const sizeDisplay = dialog.querySelector('#size-display');

    sizeSlider.addEventListener('input', () => {
      sizeDisplay.textContent = sizeSlider.value + 'px';
    });

    dialog.querySelector('#cancel-edit').addEventListener('click', () => {
      document.body.removeChild(modal);
    });

    dialog.querySelector('#save-edit').addEventListener('click', () => {
      this.savePointEdit(point, {
        name: dialog.querySelector('#edit-point-name').value,
        iconSize: parseInt(dialog.querySelector('#edit-point-size').value),
        iconColor: dialog.querySelector('#edit-point-color').value
      });
      document.body.removeChild(modal);
    });

    // 点击背景关闭
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        document.body.removeChild(modal);
      }
    });

    return modal;
  }

  savePointEdit(point, newData) {
    // 更新点位数据
    point.name = newData.name;
    if (!point.opts) point.opts = {};
    point.opts.iconSize = newData.iconSize;
    point.opts.iconColor = newData.iconColor;

    // 更新界面
    this.updatePointsList();
    this.updateRouteSelects();
    this.refreshMap();

    console.log('点位更新成功:', point);
  }

  addDragEvents(element) {
    let draggedElement = null;

    element.addEventListener('dragstart', (e) => {
      draggedElement = element;
      e.dataTransfer.setData('text/plain', element.dataset.pointId);
      element.style.opacity = '0.5';
    });

    element.addEventListener('dragend', (e) => {
      element.style.opacity = '1';
      draggedElement = null;
    });

    element.addEventListener('dragover', (e) => {
      e.preventDefault();
      if (draggedElement && draggedElement !== element) {
        element.style.borderTop = '2px solid #1b8eec';
      }
    });

    element.addEventListener('dragleave', (e) => {
      element.style.borderTop = '';
    });

    element.addEventListener('drop', (e) => {
      e.preventDefault();
      element.style.borderTop = '';

      if (draggedElement && draggedElement !== element) {
        this.reorderPoints(draggedElement.dataset.pointId, element.dataset.pointId);
      }
    });
  }

  reorderPoints(draggedId, targetId) {
    const points = TRIPS[ALL_DATA_ID].data.points;
    const draggedIndex = points.findIndex(p => p.id == draggedId);
    const targetIndex = points.findIndex(p => p.id == targetId);

    if (draggedIndex !== -1 && targetIndex !== -1) {
      // 移动元素
      const draggedPoint = points.splice(draggedIndex, 1)[0];
      points.splice(targetIndex, 0, draggedPoint);

      // 更新界面
      this.updatePointsList();
      this.refreshMap();

      console.log('点位顺序已更新');
    }
  }

  refreshMap() {
    BMAP.clearOverlays();
    draw(TRIPS[ALL_DATA_ID].data);
  }
}

// 导出工具栏实例
export default Toolbar;
