<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Baidu Map</title>
  <script src="https://api.map.baidu.com/api?v=1.0&type=webgl&ak=loGB6pNt4jQG8R4VgNBfgYajIyWw6c4W"></script>
  <script src="/static/js/Lushu.js"></script>
  <script src="bundle.js"></script>
  <link rel="stylesheet" href="/static/fonticon/iconfont.css">
  <link rel="stylesheet" href="/static/css/index.css">
</head>
<body>
<style>
  html{height:100%}
  body{height:100%;margin:0px;padding:0px}
  #map{height:100%}
  .drawing-panel {
    z-index: 999;
    position: fixed;
    top: 0;
    left: 600px;
    margin: 0;
    padding: 1rem 1rem;
    border-radius: .25rem;
    background-color: #fff;
    box-shadow: 0 2px 6px 0 rgba(27, 142, 236, 0.5);
  }
  .btn {
    width: 90px;
    height: 30px;
    float: left;
    background-color: #fff;
    color: rgba(27, 142, 236, 1);
    font-size: 14px;
    border:1px solid rgba(27, 142, 236, 1);
    border-radius: 5px;
    margin: 0 5px;
    text-align: center;
    line-height: 30px;
    list-style-type: none;
  }
  .btn:hover {
    background-color: rgba(27, 142, 236, 0.8);
    color: #fff;
  }
</style>

<div id="map" style="width: 100%; height: 100vh;"></div>
<ul class="drawing-panel" style="z-index: 99;">
  <li class = "btn" id="btn_start">动画</li>
  <li class = "btn" id="btn_pause">暂停</li>
  <li class = "btn" id="btn_resume">继续</li>
  <li style="clear: both; margin-top: 10px; color: #333;">
    <label for="curvature-slider">曲率调节: </label>
    <input type="range" id="curvature-slider" min="0.1" max="1.5" step="0.1" value="0.5" style="width: 120px;">
    <span id="curvature-value">0.5</span>
  </li>
  <li class = "btn" id="btn_refresh" style="margin-top: 5px;">刷新路线</li>
</ul>
</body>
</html>
