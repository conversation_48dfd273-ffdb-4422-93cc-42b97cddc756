<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Baidu Map</title>
  <script src="https://api.map.baidu.com/api?v=1.0&type=webgl&ak=loGB6pNt4jQG8R4VgNBfgYajIyWw6c4W"></script>
  <script src="/static/js/Lushu.js"></script>
  <script src="bundle.js"></script>
  <link rel="stylesheet" href="/static/fonticon/iconfont.css">
  <link rel="stylesheet" href="/static/css/index.css">
</head>
<body>
<style>
  html{height:100%}
  body{height:100%;margin:0px;padding:0px}
  #map{height:100%}
  .drawing-panel {
    z-index: 999;
    position: fixed;
    top: 0;
    left: 600px;
    margin: 0;
    padding: 1rem 1rem;
    border-radius: .25rem;
    background-color: #fff;
    box-shadow: 0 2px 6px 0 rgba(27, 142, 236, 0.5);
  }
  .toolbar {
    position: fixed;
    top: 0;
    right: 0;
    width: 350px;
    height: 100vh;
    background: #fff;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    z-index: 1000;
    overflow-y: auto;
    transform: translateX(100%);
    transition: transform 0.3s ease;
  }
  .toolbar.open {
    transform: translateX(0);
  }
  .toolbar-toggle {
    position: fixed;
    top: 50%;
    right: 0;
    width: 40px;
    height: 80px;
    background: #1b8eec;
    color: white;
    border: none;
    border-radius: 8px 0 0 8px;
    cursor: pointer;
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transform: translateY(-50%);
  }
  .toolbar-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
  }
  .toolbar-content {
    padding: 20px;
  }
  .section {
    margin-bottom: 30px;
  }
  .section-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
    border-bottom: 2px solid #1b8eec;
    padding-bottom: 5px;
  }
  .form-group {
    margin-bottom: 15px;
  }
  .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
  }
  .form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
  }
  .form-control:focus {
    outline: none;
    border-color: #1b8eec;
    box-shadow: 0 0 0 2px rgba(27, 142, 236, 0.2);
  }
  .btn-primary {
    background: #1b8eec;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
  }
  .btn-primary:hover {
    background: #1976d2;
  }
  .btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
  }
  .btn-danger {
    background: #dc3545;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
  }
  .item-list {
    max-height: 300px;
    overflow-y: auto;
  }
  .list-item {
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 4px;
    margin-bottom: 8px;
    background: #f9f9f9;
    cursor: move;
  }
  .list-item:hover {
    background: #f0f0f0;
  }
  .list-item-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 5px;
  }
  .list-item-title {
    font-weight: 500;
    flex: 1;
  }
  .list-item-actions {
    display: flex;
    gap: 5px;
  }
  .color-picker {
    width: 40px;
    height: 30px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
  .size-slider {
    width: 100%;
  }
  .btn {
    width: 90px;
    height: 30px;
    float: left;
    background-color: #fff;
    color: rgba(27, 142, 236, 1);
    font-size: 14px;
    border:1px solid rgba(27, 142, 236, 1);
    border-radius: 5px;
    margin: 0 5px;
    text-align: center;
    line-height: 30px;
    list-style-type: none;
  }
  .btn:hover {
    background-color: rgba(27, 142, 236, 0.8);
    color: #fff;
  }
</style>

<div id="map" style="width: 100%; height: 100vh;"></div>
<ul class="drawing-panel" style="z-index: 99;">
  <li class = "btn" id="btn_start">动画</li>
  <li class = "btn" id="btn_pause">暂停</li>
  <li class = "btn" id="btn_resume">继续</li>
</ul>
</body>
</html>
