import {getUUID, getNextId, getRandomInt} from './IDGenerator';

// 一些常量
const ALL_DATA_ID = '0000000000000000';
const DEFAULT_ROUTE_WAY = 'drive';

// 数据重置
function initData() {
  window.TRIPS = {};
  TRIPS[ALL_DATA_ID] = {
    name: '全部',
    data: {
      points: [],
      routes: [],
      tracks: []
    }
  };
}

// 添加点位
async function addPoint(point) {
  let {name, opts} = point;
  let id = getNextId();

  let geo = await getGeoFromName(name);
  TRIPS[ALL_DATA_ID].data.points.push({
    id,
    name,
    geo,
    opts,
  });

  return id;
}

// 根据地点名称获取经纬度
async function getGeoFromName(name) {
  return new Promise((resolve, reject) => {
    const geocoder = new BMapGL.Geocoder();
    geocoder.getPoint(name, (point) => {
      if (point) {
        resolve(point);
      } else {
        reject(new Error('--------未找到该地点，请重新输入--------'));
      }
    }, "中国");
  });
}

// 添加线路
function addRoute(route) {
  let {startPoint, endPoint, way, opts} = route;
  let id = getNextId();
  TRIPS[ALL_DATA_ID].data.routes.push({
    id,
    startPoint,
    endPoint,
    way: way || DEFAULT_ROUTE_WAY,
    opts,
  });

  return id;
}

// 移除点位，从【全部】移除，同时从【分组】中移除
function removePoint(pointId) {
  for (var key in TRIPS) {
    if (TRIPS[key].data && TRIPS[key].data.points) {
      TRIPS[key].data.points = TRIPS[key].data.points.filter(point => point.id !== pointId);
    }
  }
  // 同时移除相关的路线
  for (var key in TRIPS) {
    if (TRIPS[key].data && TRIPS[key].data.routes) {
      TRIPS[key].data.routes = TRIPS[key].data.routes.filter(route =>
        route.startPoint !== pointId && route.endPoint !== pointId
      );
    }
  }
}

// 移除线路，从【全部】移除，同时从【分组】中移除
function removeRoute(routeId) {
  for (var key in TRIPS) {
    if (TRIPS[key].data && TRIPS[key].data.routes) {
      TRIPS[key].data.routes = TRIPS[key].data.routes.filter(route => route.id !== routeId);
    }
  }
}

// 添加组
function addGroup(name) {
  let uuid = getUUID();
  TRIPS[uuid] = {name};
  return uuid;
}

// 给组里添加点位
function addPointToGroup(pointId, groupId) {
  let point = TRIPS[ALL_DATA_ID].data.points.find(point => point.id === pointId);
  TRIPS[groupId].data.points.push(point);
}

// 给轨迹里添加点位
function addPointToTrack(pointId) {
  let point = TRIPS[ALL_DATA_ID].data.points.find(point => point.id === pointId);
  TRIPS[ALL_DATA_ID].data.tracks.push({
    type: 'point',
    id: pointId,
  });
}

// 给组里添加路线
function addRouteToGroup(routeId, groupId) {
  let route = TRIPS[ALL_DATA_ID].data.routes.find(route => route.id === routeId);
  TRIPS[groupId].data.routes.push(route);
}

// 给轨迹里添加路线
function addRouteToTrack(routeId) {
  TRIPS[ALL_DATA_ID].data.tracks.push({
    type: 'route',
    id: routeId,
  });
}

// 从组中移除点位
function removePointFromGroup(pointId, groupId) {
  TRIPS[groupId].data.points = TRIPS[groupId].data.points.filter(point => point.id !== pointId);
}

// 从轨迹中移除点位
function removePointFromTrack(pointId) {
  TRIPS[ALL_DATA_ID].data.tracks = TRIPS[ALL_DATA_ID].data.tracks.filter(point => point.type === 'point' && point.id !== pointId);
}

// 从组中移除路线
function removeRouteFromGroup(routeId, groupId) {
  TRIPS[groupId].data.routes = TRIPS[groupId].data.routes.filter(route => route.id !== routeId);
}

// 从轨迹中移除路线
function removeRouteFromTrack(routeId) {
  TRIPS[ALL_DATA_ID].data.tracks = TRIPS[ALL_DATA_ID].data.tracks.filter(route => route.type === 'route' &&  route.id !== routeId);
}

export {
  ALL_DATA_ID,
  initData,
  addPoint,
  addRoute,
  removePoint,
  removeRoute,
  addGroup,
  addPointToGroup,
  addPointToTrack,
  addRouteToGroup,
  addRouteToTrack,
  removePointFromGroup,
  removePointFromTrack,
  removeRouteFromGroup,
  removeRouteFromTrack,
};
