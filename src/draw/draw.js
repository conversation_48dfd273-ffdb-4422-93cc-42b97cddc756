import { calculateBezierCurvePoints } from '../func/curveCalculator'
import { mergeRouteOpts } from '../func/mergeOpts'
import { getRandomInt } from '../func/IDGenerator'

// 画出点位
function drawPoints(points) {
  for (let point of points) {
    const mapPoint = new BMapGL.Point(point.geo.lng, point.geo.lat);
    const marker = new BMapGL.Marker(mapPoint, point.opts);
    BMAP.addOverlay(marker);
    pointsMap[point.id] = mapPoint;

    // 使用自定义图标大小，如果没有设置则使用默认值
    const iconSize = point.opts?.iconSize || MARKER_SIZE;
    const iconColor = point.opts?.iconColor || '#1b8eec';

    // 创建自定义图标
    const iconUrl = `/static/icons/${getRandomInt(1, 50)}.png`;
    const icon = new BMapGL.Icon(iconUrl, new BMapGL.Size(iconSize, iconSize), {
      anchor: new BMapGL.Size(iconSize / 2, iconSize / 2)
    });

    marker.setIcon(icon);

    // 设置信息窗口
    if (point.opts && point.opts.info) {
      let infoWindow = new BMapGL.InfoWindow(point.opts.info.html, point.opts.info.opts);
      infoWindowsMap[point.id] = infoWindow;

      // marker添加点击事件
      marker.addEventListener('click', function () {
        this.openInfoWindow(infoWindow);
        // 图片加载完毕重绘infoWindow
        // document.getElementById('imgDemo').onload = function () {
        //   infoWindow.redraw(); // 防止在网速较慢时生成的信息框高度比图片总高度小，导致图片部分被隐藏h'h'h
        // };
      });
    }
  }
}

// 画出路线
function drawRoutes(routes) {
  routes.forEach(route => {
    if (route.way === 'fly') {
      drawFly(route);
    } else {
      drawDrive(route);
    }
  });
}

// 画出驾车路线
function drawDrive(route) {
  const driving = new BMapGL.DrivingRoute(BMAP, mergeRouteOpts(route, true));
  driving.search(pointsMap[route.startPoint], pointsMap[route.endPoint]);
}

// 画出飞行路线
function drawFly(route) {
  // 使用路线特定的曲率值，如果没有设置则使用全局默认值
  const curvature = route.opts?.curvature || CURVATURE;
  let points = calculateBezierCurvePoints(pointsMap[route.startPoint], pointsMap[route.endPoint], curvature, NUMPOINTS);
  let polyline = new BMapGL.Polyline(points, route.opts);
  BMAP.addOverlay(polyline);
}

export default function (data) {
  drawPoints(data.points);
  drawRoutes(data.routes);
}
