import {calculateBezierCurvePoints} from '../func/curveCalculator'
import {mergeRouteOpts} from '../func/mergeOpts'
import {getRandomInt} from '../func/IDGenerator'
import {addBubble} from './bubble'

// 画出点位或路线
export default async function (data) {
  for (let track of data.tracks) {
    // 控制同一时间只能有一条动画正在展示，动画完成后再渲染点位或动画
    while (isDrawingAni) {
      await delay(500);
    }

    if (track.type === 'point') {
      let point = data.points.find(point => point.id === track.id);
      BMAP.setCenter(pointsMap[point.id]);
      await drawPoint(point);
    } else {

      // 修改状态
      isDrawingAni = true;
      // 设置中心点
      let route = data.routes.find(route => route.id === track.id);
      BMAP.setCenter(pointsMap[route.startPoint]);

      if (route.way === 'fly') {
        BMAP.setZoom(6);
        await drawFly(route);
      } else {
        await drawOther(route);
      }
    }
  }

  // 等待动画完成
  while (isDrawingAni) {
    await delay(500);
  }

  // 添加结束
  let pointId = null;
  let last = data.tracks[data.tracks.length - 1];
  if (last.type === 'point') {
    pointId = last.id;
  } else {
    pointId = last.endPoint;
  }
  addBubble(pointsMap[pointId], 'GAMEOVER！');
}

// 画出点位
async function drawPoint(point) {
  const mapPoint = pointsMap[point.id];
  const marker = new BMapGL.Marker(mapPoint, point.opts);
  BMAP.addOverlay(marker);

  // 随机设置一个图标
  marker.setIcon(new BMapGL.Icon(`/static/icons/${getRandomInt(1, 50)}.png`, new BMapGL.Size(MARKER_SIZE, MARKER_SIZE)));

  // 打开信息窗口
  if (point.opts && point.opts.info) {
    let infoWindow = infoWindowsMap[point.id];
    BMAP.openInfoWindow(infoWindow, mapPoint);

    marker.addEventListener('click', function () {
      this.openInfoWindow(infoWindow);
    });

    // 3秒后关闭
    await delay(3000);
    BMAP.closeInfoWindow();
  }
}

// 画出驾车路线
async function drawOther(route) {
  // 规划路线
  const way = getRouteWay(route);
  way.search(pointsMap[route.startPoint], pointsMap[route.endPoint]);

  // 显示动画
  way.setSearchCompleteCallback(async res => {
    if (way.getStatus() === BMAP_STATUS_SUCCESS) {
      // 获取驾车路线，均匀的取 1000 个点组成路径
      let plan = res.getPlan(0);
      let arrPois = getRoutePath(route, plan);
      arrPois.push(pointsMap[route.endPoint]);
      arrPois = selectElements(arrPois, MAX_ELEMENTS);

      // BMAP.setViewport(arrPois);
      // 计算距离，画路书
      let distance = plan.getDistance(false);
      await drawLushu(arrPois, route.opts.lushuTip, distance, `/static/icons/${getRouteIcon(route)}.png`);
    }
  });
}

// 画出飞行路线
async function drawFly(route) {
  // 使用路线特定的曲率值，如果没有设置则使用全局默认值
  const curvature = route.opts?.curvature || CURVATURE;
  let points = calculateBezierCurvePoints(pointsMap[route.startPoint], pointsMap[route.endPoint], curvature, NUMPOINTS);
  let polyline = new BMapGL.Polyline(points, route.opts);
  BMAP.addOverlay(polyline);

  // 计算距离
  let distance = 0;
  for (let i = 0; i < points.length - 1; i++) {
    distance += BMAP.getDistance(points[i + 1], points[i]);
  }

  // 画路书
  await drawLushu(points, route.opts.lushuTip, distance, `/static/icons/${getRouteIcon(route)}.png`);
}

// 数组平均抽取元素
function selectElements(arr, numElements) {
  if (arr.length <= numElements) {
    return arr;
  }

  // 计算每个元素的间隔
  const interval = (arr.length - 1) / (numElements - 1);
  const result = [];

  for (let i = 0; i < numElements; i++) {
    const index = Math.round(i * interval);
    result.push(arr[index]);
  }

  return result;
}

// 画路书
async function drawLushu(points, html, distance, iconPath) {
  let speed = distance / ANI_TIME * ANI_TIME_RATIO;
  window.LUSHU = new BMapGLLib.LuShu(BMAP, points, {
    defaultContent: html, // "信息窗口文案"
    // autoView: true, // 是否开启自动视野调整，如果开启那么路书在运动过程中会根据视野自动调整
    speed: speed,
    icon: new BMapGL.Icon(iconPath, new BMapGL.Size(ICONSIZE, ICONSIZE), {anchor: new BMapGL.Size(ICONSIZEANCHOR, ICONSIZEANCHOR)}),
    enableRotation: true, // 是否设置marker随着道路的走向进行旋转
  });
  LUSHU.start();

  // 等待动画完成
  await delay(5000);

  // 每 100ms 检查一下是否结束
  while (!LUSHU.isFinished) {
    await delay(100);
  }

  // 修改状态
  isDrawingAni = false;

}

/*--------------------------------------------------------------------------------------------------------*/

// 延迟执行
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 根据出行方式获取 BMap 对象
function getRouteWay(route) {
  switch (route.way) {
    case 'drive':
      return new BMapGL.DrivingRoute(BMAP, mergeRouteOpts(route, true));
    case 'ride':
      return new BMapGL.RidingRoute(BMAP, mergeRouteOpts(route, true));
    case 'walk':
      return new BMapGL.WalkingRoute(BMAP, mergeRouteOpts(route, true));
    case 'bus':
      return new BMapGL.TransitRoute(BMAP, mergeRouteOpts(route, true));
    default:
      return null;
  }
}

function getRouteIcon(route) {
  switch (route.way) {
    case 'drive':
      return '汽车';
    case 'ride':
      return '自行车';
    case 'walk':
      return '徒步';
    case 'bus':
      return '公交';
    case 'fly':
      return '飞机';
    default:
      return null;
  }
}

function getRoutePath(route, plan) {
  if (route.way === 'bus') {
    return getRoutePathOfBus(plan);
  } else {
    return getRoutePathOfOther(plan);
  }
}

// 获取公交路线的 path
function getRoutePathOfBus(plan) {
  let arrPois = [];
  for (let i = 0; i < plan.getNumTotal(); i++) {
    arrPois = arrPois.concat(plan.getTotal(i).getPath());
  }

  return arrPois;
}

// 获取驾车、骑行、步行的 path
function getRoutePathOfOther(plan) {
  let arrPois = [];
  for (let i = 0; i < plan.getNumRoutes(); i++) {
    arrPois = arrPois.concat(plan.getRoute(i).getPath());
  }

  return arrPois;
}
